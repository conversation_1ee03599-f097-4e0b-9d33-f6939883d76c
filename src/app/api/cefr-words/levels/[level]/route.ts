import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getCefrWordService } from '@/backend/wire';
import { withErrorHandling, withValidation } from '@/lib/api-error-middleware';
import { CefrLevel } from '@/models/cefr-word';

// GET /api/cefr-words/levels/[level] - Get words by CEFR level
const GetWordsByLevelSchema = z.object({
	limit: z.coerce.number().min(1).max(100).default(50),
	random: z.coerce.boolean().default(false),
});

export const GET = withErrorHandling(
	withValidation(GetWordsByLevelSchema, 'query')(
		async (request: NextRequest, { 
			params, 
			validatedData 
		}: { 
			params: { level: string }; 
			validatedData: z.infer<typeof GetWordsByLevelSchema> 
		}) => {
			const cefrWordService = getCefrWordService();
			
			// Validate CEFR level
			if (!Object.values(CefrLevel).includes(params.level as CefrLevel)) {
				return NextResponse.json({
					success: false,
					error: 'Invalid CEFR level. Must be one of: A1, A2, B1, B2, C1, C2',
				}, { status: 400 });
			}
			
			const level = params.level as CefrLevel;
			
			let words;
			if (validatedData.random) {
				words = await cefrWordService.getRandomWordsByLevel(level, validatedData.limit);
			} else {
				words = await cefrWordService.getCefrWordsByLevel(level, validatedData.limit);
			}
			
			return NextResponse.json({
				success: true,
				data: {
					level,
					words,
					count: words.length,
					isRandom: validatedData.random,
				},
			});
		}
	)
);
