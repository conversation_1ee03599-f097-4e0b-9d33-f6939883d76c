import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getCefrWordService } from '@/backend/wire';
import { withErrorHandling, withValidation, withAuth } from '@/lib/api-error-middleware';
import { UpdateCefrWordSchema } from '@/models/cefr-word';

// GET /api/cefr-words/[id] - Get CEFR word by ID
export const GET = withErrorHandling(
	async (request: NextRequest, { params }: { params: { id: string } }) => {
		const cefrWordService = getCefrWordService();
		
		const cefrWord = await cefrWordService.getCefrWordById(params.id);
		
		if (!cefrWord) {
			return NextResponse.json({
				success: false,
				error: 'CEFR word not found',
			}, { status: 404 });
		}
		
		return NextResponse.json({
			success: true,
			data: cefrWord,
		});
	}
);

// PUT /api/cefr-words/[id] - Update CEFR word (Admin only)
export const PUT = withErrorHandling(
	withAuth(
		withValidation(UpdateCefrWordSchema, 'body')(
			async (request: NextRequest, { params, validatedData, user }: { 
				params: { id: string }; 
				validatedData: z.infer<typeof UpdateCefrWordSchema>; 
				user: any 
			}) => {
				const cefrWordService = getCefrWordService();
				
				const auditInfo = {
					userId: user.id,
					ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
					userAgent: request.headers.get('user-agent') || 'unknown',
				};

				try {
					const cefrWord = await cefrWordService.updateCefrWord(params.id, validatedData, auditInfo);
					
					return NextResponse.json({
						success: true,
						data: cefrWord,
						message: 'CEFR word updated successfully',
					});
				} catch (error) {
					if (error instanceof Error && error.message.includes('not found')) {
						return NextResponse.json({
							success: false,
							error: 'CEFR word not found',
						}, { status: 404 });
					}
					throw error;
				}
			}
		)
	)
);

// DELETE /api/cefr-words/[id] - Delete CEFR word (Admin only)
export const DELETE = withErrorHandling(
	withAuth(
		async (request: NextRequest, { params, user }: { params: { id: string }; user: any }) => {
			const cefrWordService = getCefrWordService();
			
			const auditInfo = {
				userId: user.id,
				ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
				userAgent: request.headers.get('user-agent') || 'unknown',
			};

			try {
				await cefrWordService.deleteCefrWord(params.id, auditInfo);
				
				return NextResponse.json({
					success: true,
					message: 'CEFR word deleted successfully',
				});
			} catch (error) {
				if (error instanceof Error && error.message.includes('not found')) {
					return NextResponse.json({
						success: false,
						error: 'CEFR word not found',
					}, { status: 404 });
				}
				throw error;
			}
		}
	)
);
