import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getCefrWordService } from '@/backend/wire';
import { withErrorHandling, withValidation, withAuth } from '@/lib/api-error-middleware';
import { CefrLevel, CreateCefrWordSchema, CefrWordSearchOptions } from '@/models/cefr-word';

// GET /api/cefr-words - Search and filter CEFR words
const GetCefrWordsSchema = z.object({
	term: z.string().optional(),
	level: z.nativeEnum(CefrLevel).optional(),
	levels: z.array(z.nativeEnum(CefrLevel)).optional(),
	limit: z.coerce.number().min(1).max(100).default(50),
	offset: z.coerce.number().min(0).default(0),
});

export const GET = withErrorHandling(
	withValidation(GetCefrWordsSchema, 'query')(
		async (request: NextRequest, { validatedData }: { validatedData: z.infer<typeof GetCefrWordsSchema> }) => {
			const cefrWordService = getCefrWordService();
			
			const searchOptions: CefrWordSearchOptions = {
				term: validatedData.term,
				level: validatedData.level,
				levels: validatedData.levels,
				limit: validatedData.limit,
			};

			const words = await cefrWordService.searchCefrWords(searchOptions);
			
			return NextResponse.json({
				success: true,
				data: words,
				pagination: {
					limit: validatedData.limit,
					offset: validatedData.offset,
					total: words.length,
				},
			});
		}
	)
);

// POST /api/cefr-words - Create a new CEFR word (Admin only)
export const POST = withErrorHandling(
	withAuth(
		withValidation(CreateCefrWordSchema, 'body')(
			async (request: NextRequest, { validatedData, user }: { validatedData: z.infer<typeof CreateCefrWordSchema>; user: any }) => {
				// Check if user is admin (you might want to implement proper role checking)
				// For now, we'll allow any authenticated user to create CEFR words
				
				const cefrWordService = getCefrWordService();
				
				const auditInfo = {
					userId: user.id,
					ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
					userAgent: request.headers.get('user-agent') || 'unknown',
				};

				const cefrWord = await cefrWordService.createCefrWord(validatedData, auditInfo);
				
				return NextResponse.json({
					success: true,
					data: cefrWord,
					message: 'CEFR word created successfully',
				}, { status: 201 });
			}
		)
	)
);

// Bulk operations endpoint
const BulkCreateSchema = z.object({
	words: z.array(CreateCefrWordSchema),
	skipDuplicates: z.boolean().default(true),
});

export const PUT = withErrorHandling(
	withAuth(
		withValidation(BulkCreateSchema, 'body')(
			async (request: NextRequest, { validatedData, user }: { validatedData: z.infer<typeof BulkCreateSchema>; user: any }) => {
				const cefrWordService = getCefrWordService();
				
				const auditInfo = {
					userId: user.id,
					ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
					userAgent: request.headers.get('user-agent') || 'unknown',
				};

				const result = await cefrWordService.bulkCreateCefrWords(validatedData, auditInfo);
				
				return NextResponse.json({
					success: true,
					data: result,
					message: `Bulk operation completed. Success: ${result.success}, Failed: ${result.failed}, Duplicates: ${result.duplicates}`,
				});
			}
		)
	)
);
