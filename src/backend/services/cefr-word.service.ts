import { CefrWord } from '@prisma/client';
import { CefrWordRepository } from '@/backend/repositories/cefr-word.repository';
import {
	CefrLevel,
	CefrWordSearchOptions,
	CefrLevelStats,
	CefrWordWithMetadata,
	CreateCefrWordInput,
	UpdateCefrWordInput,
	BulkCefrWordOperation,
	CefrWordImportResult,
	CefrVocabularyRecommendation,
} from '@/models/cefr-word';
import { AuditHelper } from '@/backend/utils/audit.helper';

export interface CefrWordService {
	// Basic CRUD operations
	getCefrWordById(id: string): Promise<CefrWord | null>;
	getCefrWordByTerm(term: string): Promise<CefrWord | null>;
	createCefrWord(data: CreateCefrWordInput, auditInfo?: any): Promise<CefrWord>;
	updateCefrWord(id: string, data: UpdateCefrWordInput, auditInfo?: any): Promise<CefrWord>;
	deleteCefrWord(id: string, auditInfo?: any): Promise<void>;

	// Search and filtering
	searchCefrWords(options: CefrWordSearchOptions): Promise<CefrWord[]>;
	getCefrWordsByLevel(level: CefrLevel, limit?: number): Promise<CefrWord[]>;
	getCefrWordsByTerms(terms: string[]): Promise<CefrWord[]>;

	// Level management
	getAllCefrLevels(): Promise<string[]>;
	getCefrLevelStats(): Promise<CefrLevelStats[]>;
	getRandomWordsByLevel(level: CefrLevel, limit?: number): Promise<CefrWord[]>;

	// Bulk operations
	bulkCreateCefrWords(operation: BulkCefrWordOperation, auditInfo?: any): Promise<CefrWordImportResult>;
	bulkDeleteCefrWords(wordIds: string[], auditInfo?: any): Promise<number>;

	// Learning and recommendations
	getVocabularyRecommendations(
		currentLevel: CefrLevel,
		knownWords?: string[],
		limit?: number
	): Promise<CefrVocabularyRecommendation>;
	checkWordLevel(term: string): Promise<CefrLevel | null>;
	getWordsForLevelTransition(fromLevel: CefrLevel, toLevel: CefrLevel, limit?: number): Promise<CefrWord[]>;

	// Statistics and analytics
	getTotalWordCount(): Promise<number>;
	getWordCountByLevel(): Promise<{ level: string; count: number }[]>;
	getLevelDistribution(): Promise<{ [key in CefrLevel]: number }>;
}

export class CefrWordServiceImpl implements CefrWordService {
	constructor(
		private readonly getCefrWordRepository: () => CefrWordRepository,
		private readonly getAuditHelper: () => AuditHelper
	) {}

	async getCefrWordById(id: string): Promise<CefrWord | null> {
		return await this.getCefrWordRepository().findById(id);
	}

	async getCefrWordByTerm(term: string): Promise<CefrWord | null> {
		return await this.getCefrWordRepository().findByTerm(term);
	}

	async createCefrWord(data: CreateCefrWordInput, auditInfo?: any): Promise<CefrWord> {
		const cefrWord = await this.getCefrWordRepository().create(data);
		
		if (auditInfo) {
			await this.getAuditHelper().logAction(
				'CREATE_CEFR_WORD',
				auditInfo.userId,
				auditInfo.ipAddress,
				auditInfo.userAgent,
				{ cefrWordId: cefrWord.id, term: cefrWord.term, level: cefrWord.level }
			);
		}

		return cefrWord;
	}

	async updateCefrWord(id: string, data: UpdateCefrWordInput, auditInfo?: any): Promise<CefrWord> {
		const cefrWord = await this.getCefrWordRepository().update(id, data);
		
		if (auditInfo) {
			await this.getAuditHelper().logAction(
				'UPDATE_CEFR_WORD',
				auditInfo.userId,
				auditInfo.ipAddress,
				auditInfo.userAgent,
				{ cefrWordId: id, updates: data }
			);
		}

		return cefrWord;
	}

	async deleteCefrWord(id: string, auditInfo?: any): Promise<void> {
		if (auditInfo) {
			const cefrWord = await this.getCefrWordRepository().findById(id);
			await this.getAuditHelper().logAction(
				'DELETE_CEFR_WORD',
				auditInfo.userId,
				auditInfo.ipAddress,
				auditInfo.userAgent,
				{ cefrWordId: id, term: cefrWord?.term, level: cefrWord?.level }
			);
		}

		await this.getCefrWordRepository().delete({ id });
	}

	async searchCefrWords(options: CefrWordSearchOptions): Promise<CefrWord[]> {
		const { term, level, levels, limit = 50 } = options;

		if (term) {
			return await this.getCefrWordRepository().searchWords(term, limit);
		}

		if (level) {
			return await this.getCefrWordRepository().findByLevel(level, limit);
		}

		if (levels && levels.length > 0) {
			const allWords: CefrWord[] = [];
			for (const lvl of levels) {
				const words = await this.getCefrWordRepository().findByLevel(lvl, Math.ceil(limit / levels.length));
				allWords.push(...words);
			}
			return allWords.slice(0, limit);
		}

		// Return all words with limit
		return await this.getCefrWordRepository().find({}, limit);
	}

	async getCefrWordsByLevel(level: CefrLevel, limit = 50): Promise<CefrWord[]> {
		return await this.getCefrWordRepository().findByLevel(level, limit);
	}

	async getCefrWordsByTerms(terms: string[]): Promise<CefrWord[]> {
		return await this.getCefrWordRepository().findByTerms(terms);
	}

	async getAllCefrLevels(): Promise<string[]> {
		return await this.getCefrWordRepository().getAllLevels();
	}

	async getCefrLevelStats(): Promise<CefrLevelStats[]> {
		const wordCounts = await this.getCefrWordRepository().getWordCountByLevel();
		return wordCounts.map(({ level, count }) => ({
			level: level as CefrLevel,
			total_words: count,
		}));
	}

	async getRandomWordsByLevel(level: CefrLevel, limit = 10): Promise<CefrWord[]> {
		return await this.getCefrWordRepository().findRandomWordsByLevel(level, limit);
	}

	async bulkCreateCefrWords(operation: BulkCefrWordOperation, auditInfo?: any): Promise<CefrWordImportResult> {
		const { words, skipDuplicates = true } = operation;
		const result: CefrWordImportResult = {
			success: 0,
			failed: 0,
			duplicates: 0,
			errors: [],
		};

		for (const wordData of words) {
			try {
				// Check for existing word if skipDuplicates is true
				if (skipDuplicates) {
					const existing = await this.getCefrWordRepository().findByTerm(wordData.term);
					if (existing) {
						result.duplicates++;
						continue;
					}
				}

				await this.getCefrWordRepository().create(wordData);
				result.success++;
			} catch (error) {
				result.failed++;
				result.errors.push(`Failed to create word "${wordData.term}": ${error instanceof Error ? error.message : 'Unknown error'}`);
			}
		}

		if (auditInfo) {
			await this.getAuditHelper().logAction(
				'BULK_CREATE_CEFR_WORDS',
				auditInfo.userId,
				auditInfo.ipAddress,
				auditInfo.userAgent,
				{ result, totalWords: words.length }
			);
		}

		return result;
	}

	async bulkDeleteCefrWords(wordIds: string[], auditInfo?: any): Promise<number> {
		await this.getCefrWordRepository().delete({ id: { in: wordIds } });
		
		if (auditInfo) {
			await this.getAuditHelper().logAction(
				'BULK_DELETE_CEFR_WORDS',
				auditInfo.userId,
				auditInfo.ipAddress,
				auditInfo.userAgent,
				{ deletedWordIds: wordIds, count: wordIds.length }
			);
		}

		return wordIds.length;
	}

	async getVocabularyRecommendations(
		currentLevel: CefrLevel,
		knownWords: string[] = [],
		limit = 20
	): Promise<CefrVocabularyRecommendation> {
		// Get words for current level
		const currentLevelWords = await this.getCefrWordRepository().findByLevel(currentLevel, limit);
		
		// Filter out known words
		const recommendedWords = currentLevelWords.filter(word => !knownWords.includes(word.term));

		// Get next level preview
		const nextLevel = this.getNextLevel(currentLevel);
		const nextLevelWords = nextLevel 
			? await this.getCefrWordRepository().findByLevel(nextLevel, Math.ceil(limit / 2))
			: [];

		// Get mastery gaps (words from previous levels that might not be known)
		const previousLevel = this.getPreviousLevel(currentLevel);
		const masteryGaps = previousLevel
			? await this.getCefrWordRepository().findRandomWordsByLevel(previousLevel, Math.ceil(limit / 4))
			: [];

		return {
			current_level: currentLevel,
			recommended_words: recommendedWords.slice(0, limit),
			next_level_preview: nextLevelWords,
			mastery_gaps: masteryGaps,
		};
	}

	async checkWordLevel(term: string): Promise<CefrLevel | null> {
		const cefrWord = await this.getCefrWordRepository().findByTerm(term);
		return cefrWord ? (cefrWord.level as CefrLevel) : null;
	}

	async getWordsForLevelTransition(fromLevel: CefrLevel, toLevel: CefrLevel, limit = 30): Promise<CefrWord[]> {
		// Get words from the target level
		return await this.getCefrWordRepository().findByLevel(toLevel, limit);
	}

	async getTotalWordCount(): Promise<number> {
		const allWords = await this.getCefrWordRepository().find({});
		return allWords.length;
	}

	async getWordCountByLevel(): Promise<{ level: string; count: number }[]> {
		return await this.getCefrWordRepository().getWordCountByLevel();
	}

	async getLevelDistribution(): Promise<{ [key in CefrLevel]: number }> {
		const wordCounts = await this.getCefrWordRepository().getWordCountByLevel();
		const distribution = {} as { [key in CefrLevel]: number };
		
		// Initialize all levels with 0
		Object.values(CefrLevel).forEach(level => {
			distribution[level] = 0;
		});

		// Fill in actual counts
		wordCounts.forEach(({ level, count }) => {
			if (level in CefrLevel) {
				distribution[level as CefrLevel] = count;
			}
		});

		return distribution;
	}

	private getNextLevel(currentLevel: CefrLevel): CefrLevel | null {
		const levels = Object.values(CefrLevel);
		const currentIndex = levels.indexOf(currentLevel);
		return currentIndex < levels.length - 1 ? levels[currentIndex + 1] : null;
	}

	private getPreviousLevel(currentLevel: CefrLevel): CefrLevel | null {
		const levels = Object.values(CefrLevel);
		const currentIndex = levels.indexOf(currentLevel);
		return currentIndex > 0 ? levels[currentIndex - 1] : null;
	}
}
